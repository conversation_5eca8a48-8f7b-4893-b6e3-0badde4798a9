💬 Language & Explanation Style
	•	Always use English for responses unless otherwise specified.
	•	Explain Elixir or Typescript code clearly, including:
	•	Purpose of the function/module.
	•	How it interacts with Phoenix/Nebulex/Redis.
	•	Lifecycle implications (cache invalidation, process restart).
	•	If the response contains complex macros or DSLs, include a breakdown.

🧠 Thought Process and Debugging
	•	For every error or exception, suggest:
	•	Most likely cause.
	•	Diagnostic steps (e.g., IO.inspect, Logger.debug).
	•	Fixes based on the context (Phoenix, Redis, Oban, etc.).

🔄 Interaction Preference
	•	Summarize long outputs with a “TL;DR” block if >30 lines.
	•	Ask clarifying questions first if the request is ambiguous.
	•	Provide working code examples unless the request is theoretical.
🛠️ Workspace Guideline Examples

These define how the assistant should interpret, write, and enforce conventions in your Elixir Phoenix project.

📚 Libraries & Frameworks
	•	Use:
	•	Nebulex for caching.
	•	Redis as the primary cache backend.
	•	Oban for background job processing and scheduling.
	•	Redix only for direct Redis commands (e.g., pub/sub or pipelines).
	•	Avoid:
	•	Cachex, unless Nebulex is unsuitable.
	•	Quantum for scheduling — use Oban.Plugins.Cron.

⸻

🔁 Design Patterns
	•	For caching, use:
	•	Nebulex.get_or_set/4 for lazy cache initialization.
	•	Versioned cache keys: "entity:#{id}:v1".
	•	For job queues, follow:
	•	Oban.Worker modules must be named with the suffix Worker.
	•	Jobs must be idempotent and retriable.
	•	Prefer supervised processes for persistent cache clients.

⸻

🚫 Anti-Patterns to Avoid
	•	❌ Direct Redis commands unless absolutely necessary.
	•	❌ Global ETS tables for shared caching (prefer Nebulex Local or Redis).
	•	❌ Using Task.async/await for periodic tasks — use Oban scheduling.

⸻

🧱 Project Structure & Naming
	•	Cache modules go under MyApp.Cache, e.g., MyApp.Cache.UserCache
	•	Oban Workers go under MyApp.Workers, e.g., MyApp.Workers.RebuildIndexWorker
	•	Scheduled jobs use Oban.Plugins.Cron with cron expressions in config.exs.
🧪 Testing Preferences
	•	For Nebulex, mock Redis via in-memory local adapter in tests.
	•	For Oban, use Oban.Testing.inline() or Oban.Testing.assert_enqueued().
	•	All tests must be written in ExUnit, not ESpec.

⸻

📈 Observability & DevOps
	•	Use PromEx or Telemetry to monitor:
	•	Cache hit/miss ratios.
	•	Oban job performance (failures, durations).
	•	Use LiveDashboard with:
	•	Oban integration (:oban_dashboard)
	•	Nebulex stats via telemetry hooks.

⸻

🔄 Deployment & Scaling
	•	Redis must be externally hosted or containerized (not local-only).
	•	All scheduled tasks must be idempotent and safe to run on multiple nodes.
	•	Prefer stateless Phoenix nodes for cluster compatibility.